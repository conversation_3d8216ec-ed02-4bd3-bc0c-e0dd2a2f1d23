"use strict";
const common_vendor = require("../common/vendor.js");
const utils_http = require("../utils/http.js");
const getCode = () => {
  return utils_http.http.get("/user/getCode");
};
const login = (loginForm) => {
  return utils_http.http.post("/user/login", loginForm);
};
const getUserInfo = () => {
  return utils_http.http.get("/user/info");
};
const logout = () => {
  return utils_http.http.get("/user/logout");
};
const updateInfo = (data) => {
  return utils_http.http.post("/user/updateInfo", data);
};
const updateUserPassword = (data) => {
  return utils_http.http.post("/user/updatePassword", data);
};
const getWxCode = () => {
  return new Promise((resolve, reject) => {
    common_vendor.index.login({
      provider: "weixin",
      success: (res) => resolve(res),
      fail: (err) => reject(new Error(err))
    });
  });
};
const wxLogin = (data) => {
  return utils_http.http.post("/user/wxLogin", data);
};
exports.getCode = getCode;
exports.getUserInfo = getUserInfo;
exports.getWxCode = getWxCode;
exports.login = login;
exports.logout = logout;
exports.updateInfo = updateInfo;
exports.updateUserPassword = updateUserPassword;
exports.wxLogin = wxLogin;
