"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const interceptors_route = require("./interceptors/route.js");
const interceptors_request = require("./interceptors/request.js");
const interceptors_prototype = require("./interceptors/prototype.js");
const store_index = require("./store/index.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/about/about.js";
  "./pages/login/index.js";
  "./pages/mine/index.js";
  "./pages/test/index.js";
  "./pages/mine/about/index.js";
  "./pages/mine/info/index.js";
  "./pages/mine/password/index.js";
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "App",
  setup(__props) {
    common_vendor.onLaunch(() => {
      console.log("App Launch");
    });
    common_vendor.onShow(() => {
      console.log("App Show");
    });
    common_vendor.onHide(() => {
      console.log("App Hide");
    });
    return () => {
    };
  }
});
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  app.use(store_index.store);
  app.use(interceptors_route.routeInterceptor);
  app.use(interceptors_request.requestInterceptor);
  app.use(interceptors_prototype.prototypeInterceptor);
  app.use(common_vendor.VueQueryPlugin);
  app.component("layout-default-uni", Layout_Default_Uni);
  app.component("layout-demo-uni", Layout_Demo_Uni);
  return {
    app
  };
}
const Layout_Default_Uni = () => "./layouts/default.js";
const Layout_Demo_Uni = () => "./layouts/demo.js";
createApp().app.mount("#app");
exports.createApp = createApp;
