"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _easycom_wd_navbar2 = common_vendor.resolveComponent("wd-navbar");
  _easycom_wd_navbar2();
}
const _easycom_wd_navbar = () => "../../node-modules/wot-design-uni/components/wd-navbar/wd-navbar.js";
if (!Math) {
  _easycom_wd_navbar();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "fg-navbar",
  props: {
    leftText: { default: "返回" },
    rightText: { default: "" },
    leftArrow: { type: Boolean, default: true },
    bordered: { type: Boolean, default: true },
    fixed: { type: Boolean, default: false },
    placeholder: { type: Boolean, default: true },
    zIndex: { default: 1 },
    safeAreaInsetTop: { type: <PERSON>ole<PERSON>, default: true },
    leftDisabled: { type: Boolean, default: false },
    rightDisabled: { type: Boolean, default: false }
  },
  setup(__props) {
    function handleClickLeft() {
      common_vendor.index.navigateBack({
        fail() {
          common_vendor.index.reLaunch({
            url: "/pages/index/index"
          });
        }
      });
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(handleClickLeft),
        b: common_vendor.p({
          ["left-text"]: _ctx.leftText,
          ["right-text"]: _ctx.rightText,
          ["left-arrow"]: _ctx.leftArrow,
          bordered: _ctx.bordered,
          fixed: _ctx.fixed,
          placeholder: _ctx.placeholder,
          ["z-index"]: _ctx.zIndex,
          ["safe-area-inset-top"]: _ctx.safeAreaInsetTop,
          ["left-disabled"]: _ctx.leftDisabled,
          ["right-disabled"]: _ctx.rightDisabled
        })
      };
    };
  }
});
wx.createComponent(_sfc_main);
