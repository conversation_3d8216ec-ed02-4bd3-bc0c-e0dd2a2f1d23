"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _easycom_wd_popup2 = common_vendor.resolveComponent("wd-popup");
  _easycom_wd_popup2();
}
const _easycom_wd_popup = () => "../../node-modules/wot-design-uni/components/wd-popup/wd-popup.js";
if (!Math) {
  _easycom_wd_popup();
}
const __default__ = {
  name: "privacy-popup",
  options: {
    virtualHost: true,
    addGlobalClass: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: {
    title: { default: "用户隐私保护提示" },
    desc: { default: "感谢您使用本应用，您使用本应用的服务之前请仔细阅读并同意" },
    subDesc: { default: "。当您点击同意并开始时用产品服务时，即表示你已理解并同息该条款内容，该条款将对您产生法律约束力。如您拒绝，将无法使用相应服务。" },
    protocol: { default: "《用户隐私保护指引》" }
  },
  emits: ["agree", "disagree"],
  setup(__props, { emit: __emit }) {
    const showPopup = common_vendor.ref(false);
    const privacyResolves = common_vendor.ref(/* @__PURE__ */ new Set());
    const privacyHandler = (resolve) => {
      showPopup.value = true;
      privacyResolves.value.add(resolve);
    };
    const emit = __emit;
    common_vendor.onBeforeMount(() => {
      if (common_vendor.wx$1.onNeedPrivacyAuthorization) {
        common_vendor.wx$1.onNeedPrivacyAuthorization((resolve) => {
          if (typeof privacyHandler === "function") {
            privacyHandler(resolve);
          }
        });
      }
    });
    function handleAgree() {
      showPopup.value = false;
      privacyResolves.value.forEach((resolve) => {
        resolve({
          event: "agree",
          buttonId: "agree-btn"
        });
      });
      privacyResolves.value.clear();
      emit("agree");
    }
    function handleDisagree() {
      showPopup.value = false;
      privacyResolves.value.forEach((resolve) => {
        resolve({
          event: "disagree"
        });
      });
      privacyResolves.value.clear();
    }
    function openPrivacyContract() {
      common_vendor.wx$1.openPrivacyContract({
        success: (res) => {
          console.log("openPrivacyContract success");
        },
        fail: (res) => {
          console.error("openPrivacyContract fail", res);
        }
      });
    }
    function handleClose() {
      privacyResolves.value.clear();
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.t(_ctx.title),
        b: common_vendor.t(_ctx.desc),
        c: common_vendor.t(_ctx.protocol),
        d: common_vendor.o(openPrivacyContract),
        e: common_vendor.t(_ctx.subDesc),
        f: common_vendor.o(handleDisagree),
        g: common_vendor.o(handleAgree),
        h: common_vendor.o(handleClose),
        i: common_vendor.o(($event) => showPopup.value = $event),
        j: common_vendor.p({
          ["close-on-click-modal"]: false,
          ["custom-class"]: "wd-privacy-popup",
          modelValue: showPopup.value
        })
      };
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-521c7c1b"]]);
wx.createComponent(Component);
