<view class="data-v-521c7c1b"><wd-popup wx:if="{{j}}" class="data-v-521c7c1b" u-s="{{['d']}}" bindclose="{{h}}" u-i="521c7c1b-0" bind:__l="__l" bindupdateModelValue="{{i}}" u-p="{{j}}"><view class="wd-privacy-popup__header data-v-521c7c1b"><view class="wd-picker__title data-v-521c7c1b">{{a}}</view></view><view class="wd-privacy-popup__container data-v-521c7c1b"><text class="data-v-521c7c1b">{{b}}</text><text class="wd-privacy-popup__container-protocol data-v-521c7c1b" bindtap="{{d}}">{{c}}</text><text class="data-v-521c7c1b">{{e}}</text></view><view class="wd-privacy-popup__footer data-v-521c7c1b"><button class="wd-privacy-popup__footer-disagree wd-button is-block is-round is-medium is-plain data-v-521c7c1b" id="disagree-btn" bindtap="{{f}}"> 拒绝 </button><button class="wd-privacy-popup__footer-agree wd-button is-primary is-block is-round is-medium data-v-521c7c1b" id="agree-btn" open-type="agreePrivacyAuthorization" bindagreeprivacyauthorization="{{g}}"> 同意 </button></view></wd-popup></view>