"use strict";
const common_vendor = require("../common/vendor.js");
const utils_index = require("../utils/index.js");
const VITE_UPLOAD_BASEURL = `${utils_index.getEnvBaseUploadUrl()}`;
function useUpload(formData = {}) {
  const loading = common_vendor.ref(false);
  const error = common_vendor.ref(false);
  const data = common_vendor.ref();
  const run = () => {
    common_vendor.index.chooseMedia({
      count: 1,
      mediaType: ["image"],
      success: (res) => {
        loading.value = true;
        const tempFilePath = res.tempFiles[0].tempFilePath;
        uploadFile({ tempFilePath, formData, data, error, loading });
      },
      fail: (err) => {
        console.error("uni.chooseMedia err->", err);
        error.value = true;
      }
    });
  };
  return { loading, error, data, run };
}
function uploadFile({ tempFilePath, formData, data, error, loading }) {
  common_vendor.index.uploadFile({
    url: VITE_UPLOAD_BASEURL,
    filePath: tempFilePath,
    name: "file",
    formData,
    success: (uploadFileRes) => {
      data.value = uploadFileRes.data;
    },
    fail: (err) => {
      console.error("uni.uploadFile err->", err);
      error.value = true;
    },
    complete: () => {
      loading.value = false;
    }
  });
}
exports.useUpload = useUpload;
