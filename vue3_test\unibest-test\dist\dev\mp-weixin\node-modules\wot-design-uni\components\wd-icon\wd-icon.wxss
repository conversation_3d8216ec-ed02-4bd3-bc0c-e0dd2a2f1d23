/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
 * UI规范基础变量
 */
/*----------------------------------------- Theme color. start ----------------------------------------*/
/* 主题颜色 */
/* 辅助色 */
/* 文字颜色（默认浅色背景下 */
/* 暗黑模式 */
/* 图形颜色 */
/*----------------------------------------- Theme color. end -------------------------------------------*/
/*-------------------------------- Theme color application size.  start --------------------------------*/
/* 文字字号 */
/* 文字字重 */
/* 尺寸 */
/*-------------------------------- Theme color application size.  end --------------------------------*/
/* component var */
/* action-sheet */
/* badge */
/* button */
/* cell */
/* calendar */
/* checkbox */
/* collapse */
/* divider */
/* drop-menu */
/* input-number */
/* input */
/* textarea */
/* loadmore */
/* message-box */
/* notice-bar */
/* pagination */
/* picker */
/* col-picker */
/* overlay */
/* popup */
/* progress */
/* radio */
/* search */
/* slider */
/* sort-button */
/* steps */
/* switch */
/* tabs */
/* tag */
/* toast */
/* loading */
/* tooltip */
/* popover */
/* grid-item */
/* statustip */
/* card */
/* upload */
/* curtain */
/* notify */
/* skeleton */
/* circle */
/* swiper */
/* swiper-nav */
/* segmented */
/* tabbar */
/* tabbar-item */
/* navbar */
/* navbar-capsule */
/* table */
/* sidebar */
/* sidebar-item */
/* fab */
/* count-down */
/* keyboard */
/* number-keyboard */
/* passwod-input */
/* form-item */
/* backtop */
/* index-bar */
/* text */
/* video-preview */
/* img-cropper */
/* floating-panel */
/* signature */
/**
 * 混合宏
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/**
 * 辅助函数
 */
/**
 * SCSS 配置项：命名空间以及BEM
 */
/* 转换成字符串 */
/* 判断是否存在 Modifier */
/* 判断是否存在伪类 */
/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
/**
  * BEM，定义块（b)
  */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 此方法用于生成穿透样式 */
/* 定义元素（e），对于伪类，会自动将 e 嵌套在 伪类 底下 */
/* 定义状态（m） */
/* 定义状态（m） */
/* 对于需要需要嵌套在 m 底下的 e，调用这个混合宏，一般在切换整个组件的状态，如切换颜色的时候 */
/* 状态，生成 is-$state 类名 */
/**
  * 常用混合宏
  */
/* 单行超出隐藏 */
/* 多行超出隐藏 */
/* 清除浮动 */
/* 0.5px 边框 指定方向*/
/* 0.5px 边框 环绕 */
/**
  * 三角形实现尖角样式，适用于背景透明情况
  * @param $size 三角形高，底边为 $size * 2
  * @param $bg 三角形背景颜色
  */
/**
  * 正方形实现尖角样式，适用于背景不透明情况
  * @param $size 正方形边长
  * @param $bg 正方形背景颜色
  * @param $z-index z-index属性值，不得大于外部包裹器
  * @param $box-shadow 阴影
*/
@font-face {
  font-family: "wd-icons";
  src: url("https://at.alicdn.com/t/c/font_4245058_s5cpwl25n7o.woff2?t=1696817709651") format("woff2"), url("https://at.alicdn.com/t/c/font_4245058_s5cpwl25n7o.woff?t=1696817709651") format("woff"), url("https://at.alicdn.com/t/c/font_4245058_s5cpwl25n7o.ttf?t=1696817709651") format("truetype");
  font-weight: normal;
  font-style: normal;
}
.wd-icon.data-v-d4a8410a {
  display: inline-block;
  font-family: "wd-icons" !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.wd-icon--image.data-v-d4a8410a {
  width: 1em;
  height: 1em;
}
.wd-icon__image.data-v-d4a8410a {
  width: 100%;
  height: 100%;
}
.wd-icon-usergroup-clear.data-v-d4a8410a:before {
  content: "\e739";
}
.wd-icon-user-circle.data-v-d4a8410a:before {
  content: "\e73a";
}
.wd-icon-user-talk.data-v-d4a8410a:before {
  content: "\e73b";
}
.wd-icon-user-clear.data-v-d4a8410a:before {
  content: "\e73c";
}
.wd-icon-user.data-v-d4a8410a:before {
  content: "\e73d";
}
.wd-icon-usergroup-add.data-v-d4a8410a:before {
  content: "\e73e";
}
.wd-icon-usergroup.data-v-d4a8410a:before {
  content: "\e73f";
}
.wd-icon-user-add.data-v-d4a8410a:before {
  content: "\e740";
}
.wd-icon-user-avatar.data-v-d4a8410a:before {
  content: "\e741";
}
.wd-icon-pointing-hand.data-v-d4a8410a:before {
  content: "\e742";
}
.wd-icon-cursor.data-v-d4a8410a:before {
  content: "\e743";
}
.wd-icon-fullsreen.data-v-d4a8410a:before {
  content: "\e72c";
}
.wd-icon-cloud-download.data-v-d4a8410a:before {
  content: "\e72d";
}
.wd-icon-chevron-down-rectangle.data-v-d4a8410a:before {
  content: "\e72e";
}
.wd-icon-edit.data-v-d4a8410a:before {
  content: "\e72f";
}
.wd-icon-fullscreen-exit.data-v-d4a8410a:before {
  content: "\e730";
}
.wd-icon-circle1.data-v-d4a8410a:before {
  content: "\e731";
}
.wd-icon-close-normal.data-v-d4a8410a:before {
  content: "\e732";
}
.wd-icon-browse.data-v-d4a8410a:before {
  content: "\e733";
}
.wd-icon-browse-off.data-v-d4a8410a:before {
  content: "\e734";
}
.wd-icon-chevron-up-rectangle.data-v-d4a8410a:before {
  content: "\e735";
}
.wd-icon-add-rectangle.data-v-d4a8410a:before {
  content: "\e736";
}
.wd-icon-add1.data-v-d4a8410a:before {
  content: "\e737";
}
.wd-icon-add-circle1.data-v-d4a8410a:before {
  content: "\e738";
}
.wd-icon-download1.data-v-d4a8410a:before {
  content: "\e71c";
}
.wd-icon-link.data-v-d4a8410a:before {
  content: "\e71d";
}
.wd-icon-edit-1.data-v-d4a8410a:before {
  content: "\e71e";
}
.wd-icon-jump.data-v-d4a8410a:before {
  content: "\e71f";
}
.wd-icon-chevron-down-circle.data-v-d4a8410a:before {
  content: "\e720";
}
.wd-icon-delete1.data-v-d4a8410a:before {
  content: "\e721";
}
.wd-icon-filter-clear.data-v-d4a8410a:before {
  content: "\e722";
}
.wd-icon-check-rectangle-filled.data-v-d4a8410a:before {
  content: "\e723";
}
.wd-icon-minus-circle-filled.data-v-d4a8410a:before {
  content: "\e724";
}
.wd-icon-play.data-v-d4a8410a:before {
  content: "\e725";
}
.wd-icon-pause-circle-filled.data-v-d4a8410a:before {
  content: "\e726";
}
.wd-icon-filter1.data-v-d4a8410a:before {
  content: "\e727";
}
.wd-icon-move.data-v-d4a8410a:before {
  content: "\e728";
}
.wd-icon-login.data-v-d4a8410a:before {
  content: "\e729";
}
.wd-icon-minus-circle.data-v-d4a8410a:before {
  content: "\e72a";
}
.wd-icon-close-circle.data-v-d4a8410a:before {
  content: "\e72b";
}
.wd-icon-logout.data-v-d4a8410a:before {
  content: "\e70b";
}
.wd-icon-search1.data-v-d4a8410a:before {
  content: "\e70c";
}
.wd-icon-pause-circle.data-v-d4a8410a:before {
  content: "\e70d";
}
.wd-icon-play-circle.data-v-d4a8410a:before {
  content: "\e70e";
}
.wd-icon-more1.data-v-d4a8410a:before {
  content: "\e70f";
}
.wd-icon-minus-rectangle.data-v-d4a8410a:before {
  content: "\e710";
}
.wd-icon-stop.data-v-d4a8410a:before {
  content: "\e711";
}
.wd-icon-scan1.data-v-d4a8410a:before {
  content: "\e712";
}
.wd-icon-close-rectangle.data-v-d4a8410a:before {
  content: "\e713";
}
.wd-icon-rollback.data-v-d4a8410a:before {
  content: "\e714";
}
.wd-icon-a-order-adjustmentcolumn.data-v-d4a8410a:before {
  content: "\e715";
}
.wd-icon-pause.data-v-d4a8410a:before {
  content: "\e716";
}
.wd-icon-ellipsis.data-v-d4a8410a:before {
  content: "\e717";
}
.wd-icon-cloud-upload.data-v-d4a8410a:before {
  content: "\e718";
}
.wd-icon-stop-circle-filled.data-v-d4a8410a:before {
  content: "\e719";
}
.wd-icon-clear.data-v-d4a8410a:before {
  content: "\e71a";
}
.wd-icon-remove.data-v-d4a8410a:before {
  content: "\e71b";
}
.wd-icon-zoom-out.data-v-d4a8410a:before {
  content: "\e6fb";
}
.wd-icon-thumb-down.data-v-d4a8410a:before {
  content: "\e6fc";
}
.wd-icon-setting1.data-v-d4a8410a:before {
  content: "\e6fd";
}
.wd-icon-save.data-v-d4a8410a:before {
  content: "\e6fe";
}
.wd-icon-unfold-more.data-v-d4a8410a:before {
  content: "\e6ff";
}
.wd-icon-zoom-in.data-v-d4a8410a:before {
  content: "\e700";
}
.wd-icon-thumb-up.data-v-d4a8410a:before {
  content: "\e701";
}
.wd-icon-unfold-less.data-v-d4a8410a:before {
  content: "\e702";
}
.wd-icon-play-circle-filled.data-v-d4a8410a:before {
  content: "\e703";
}
.wd-icon-poweroff.data-v-d4a8410a:before {
  content: "\e704";
}
.wd-icon-share.data-v-d4a8410a:before {
  content: "\e705";
}
.wd-icon-refresh1.data-v-d4a8410a:before {
  content: "\e706";
}
.wd-icon-link-unlink.data-v-d4a8410a:before {
  content: "\e707";
}
.wd-icon-upload.data-v-d4a8410a:before {
  content: "\e708";
}
.wd-icon-rectangle.data-v-d4a8410a:before {
  content: "\e709";
}
.wd-icon-stop-circle.data-v-d4a8410a:before {
  content: "\e70a";
}
.wd-icon-backtop-rectangle.data-v-d4a8410a:before {
  content: "\e6ea";
}
.wd-icon-caret-down.data-v-d4a8410a:before {
  content: "\e6eb";
}
.wd-icon-arrow-left1.data-v-d4a8410a:before {
  content: "\e6ec";
}
.wd-icon-help-circle.data-v-d4a8410a:before {
  content: "\e6ed";
}
.wd-icon-help-circle-filled.data-v-d4a8410a:before {
  content: "\e6ee";
}
.wd-icon-time-filled.data-v-d4a8410a:before {
  content: "\e6ef";
}
.wd-icon-close-circle-filled.data-v-d4a8410a:before {
  content: "\e6f0";
}
.wd-icon-info-circle.data-v-d4a8410a:before {
  content: "\e6f1";
}
.wd-icon-info-circle-filled.data-v-d4a8410a:before {
  content: "\e6f2";
}
.wd-icon-check1.data-v-d4a8410a:before {
  content: "\e6f3";
}
.wd-icon-help.data-v-d4a8410a:before {
  content: "\e6f4";
}
.wd-icon-error.data-v-d4a8410a:before {
  content: "\e6f5";
}
.wd-icon-check-circle.data-v-d4a8410a:before {
  content: "\e6f6";
}
.wd-icon-error-circle-filled.data-v-d4a8410a:before {
  content: "\e6f7";
}
.wd-icon-error-circle.data-v-d4a8410a:before {
  content: "\e6f8";
}
.wd-icon-check-rectangle.data-v-d4a8410a:before {
  content: "\e6f9";
}
.wd-icon-check-circle-filled.data-v-d4a8410a:before {
  content: "\e6fa";
}
.wd-icon-chevron-up.data-v-d4a8410a:before {
  content: "\e6da";
}
.wd-icon-chevron-up-circle.data-v-d4a8410a:before {
  content: "\e6db";
}
.wd-icon-chevron-right.data-v-d4a8410a:before {
  content: "\e6dc";
}
.wd-icon-arrow-down-rectangle.data-v-d4a8410a:before {
  content: "\e6dd";
}
.wd-icon-caret-up-small.data-v-d4a8410a:before {
  content: "\e6de";
}
.wd-icon-chevron-right-rectangle.data-v-d4a8410a:before {
  content: "\e6df";
}
.wd-icon-caret-right-small.data-v-d4a8410a:before {
  content: "\e6e0";
}
.wd-icon-arrow-right1.data-v-d4a8410a:before {
  content: "\e6e1";
}
.wd-icon-backtop.data-v-d4a8410a:before {
  content: "\e6e2";
}
.wd-icon-arrow-up1.data-v-d4a8410a:before {
  content: "\e6e3";
}
.wd-icon-caret-up.data-v-d4a8410a:before {
  content: "\e6e4";
}
.wd-icon-backward.data-v-d4a8410a:before {
  content: "\e6e5";
}
.wd-icon-arrow-down1.data-v-d4a8410a:before {
  content: "\e6e6";
}
.wd-icon-chevron-left.data-v-d4a8410a:before {
  content: "\e6e7";
}
.wd-icon-caret-right.data-v-d4a8410a:before {
  content: "\e6e8";
}
.wd-icon-caret-left.data-v-d4a8410a:before {
  content: "\e6e9";
}
.wd-icon-page-last.data-v-d4a8410a:before {
  content: "\e6c9";
}
.wd-icon-next.data-v-d4a8410a:before {
  content: "\e6ca";
}
.wd-icon-swap.data-v-d4a8410a:before {
  content: "\e6cb";
}
.wd-icon-round.data-v-d4a8410a:before {
  content: "\e6cc";
}
.wd-icon-previous.data-v-d4a8410a:before {
  content: "\e6cd";
}
.wd-icon-enter.data-v-d4a8410a:before {
  content: "\e6ce";
}
.wd-icon-chevron-down.data-v-d4a8410a:before {
  content: "\e6cf";
}
.wd-icon-caret-down-small.data-v-d4a8410a:before {
  content: "\e6d0";
}
.wd-icon-swap-right.data-v-d4a8410a:before {
  content: "\e6d1";
}
.wd-icon-chevron-left-circle.data-v-d4a8410a:before {
  content: "\e6d2";
}
.wd-icon-caret-left-small.data-v-d4a8410a:before {
  content: "\e6d3";
}
.wd-icon-chevron-right-circle.data-v-d4a8410a:before {
  content: "\e6d4";
}
.wd-icon-a-chevron-leftdouble.data-v-d4a8410a:before {
  content: "\e6d5";
}
.wd-icon-chevron-left-rectangle.data-v-d4a8410a:before {
  content: "\e6d6";
}
.wd-icon-a-chevron-rightdouble.data-v-d4a8410a:before {
  content: "\e6d7";
}
.wd-icon-page-first.data-v-d4a8410a:before {
  content: "\e6d8";
}
.wd-icon-forward.data-v-d4a8410a:before {
  content: "\e6d9";
}
.wd-icon-view-column.data-v-d4a8410a:before {
  content: "\e6b9";
}
.wd-icon-view-module.data-v-d4a8410a:before {
  content: "\e6ba";
}
.wd-icon-format-vertical-align-right.data-v-d4a8410a:before {
  content: "\e6bb";
}
.wd-icon-view-list.data-v-d4a8410a:before {
  content: "\e6bc";
}
.wd-icon-order-descending.data-v-d4a8410a:before {
  content: "\e6bd";
}
.wd-icon-format-horizontal-align-bottom.data-v-d4a8410a:before {
  content: "\e6be";
}
.wd-icon-queue.data-v-d4a8410a:before {
  content: "\e6bf";
}
.wd-icon-menu-fold.data-v-d4a8410a:before {
  content: "\e6c0";
}
.wd-icon-menu-unfold.data-v-d4a8410a:before {
  content: "\e6c1";
}
.wd-icon-format-horizontal-align-top.data-v-d4a8410a:before {
  content: "\e6c2";
}
.wd-icon-a-rootlist.data-v-d4a8410a:before {
  content: "\e6c3";
}
.wd-icon-order-ascending.data-v-d4a8410a:before {
  content: "\e6c4";
}
.wd-icon-format-vertical-align-left.data-v-d4a8410a:before {
  content: "\e6c5";
}
.wd-icon-format-horizontal-align-center.data-v-d4a8410a:before {
  content: "\e6c6";
}
.wd-icon-format-vertical-align-center.data-v-d4a8410a:before {
  content: "\e6c7";
}
.wd-icon-swap-left.data-v-d4a8410a:before {
  content: "\e6c8";
}
.wd-icon-flag.data-v-d4a8410a:before {
  content: "\e6aa";
}
.wd-icon-code.data-v-d4a8410a:before {
  content: "\e6ab";
}
.wd-icon-cart.data-v-d4a8410a:before {
  content: "\e6ac";
}
.wd-icon-attach.data-v-d4a8410a:before {
  content: "\e6ad";
}
.wd-icon-chart.data-v-d4a8410a:before {
  content: "\e6ae";
}
.wd-icon-creditcard.data-v-d4a8410a:before {
  content: "\e6af";
}
.wd-icon-calendar.data-v-d4a8410a:before {
  content: "\e6b0";
}
.wd-icon-app.data-v-d4a8410a:before {
  content: "\e6b1";
}
.wd-icon-books.data-v-d4a8410a:before {
  content: "\e6b2";
}
.wd-icon-barcode.data-v-d4a8410a:before {
  content: "\e6b3";
}
.wd-icon-chart-pie.data-v-d4a8410a:before {
  content: "\e6b4";
}
.wd-icon-chart-bar.data-v-d4a8410a:before {
  content: "\e6b5";
}
.wd-icon-chart-bubble.data-v-d4a8410a:before {
  content: "\e6b6";
}
.wd-icon-bulletpoint.data-v-d4a8410a:before {
  content: "\e6b7";
}
.wd-icon-bianjiliebiao.data-v-d4a8410a:before {
  content: "\e6b8";
}
.wd-icon-image.data-v-d4a8410a:before {
  content: "\e69a";
}
.wd-icon-laptop.data-v-d4a8410a:before {
  content: "\e69b";
}
.wd-icon-hourglass.data-v-d4a8410a:before {
  content: "\e69c";
}
.wd-icon-call.data-v-d4a8410a:before {
  content: "\e69d";
}
.wd-icon-mobile-vibrate.data-v-d4a8410a:before {
  content: "\e69e";
}
.wd-icon-mail.data-v-d4a8410a:before {
  content: "\e69f";
}
.wd-icon-notification-filled.data-v-d4a8410a:before {
  content: "\e6a0";
}
.wd-icon-desktop.data-v-d4a8410a:before {
  content: "\e6a1";
}
.wd-icon-history.data-v-d4a8410a:before {
  content: "\e6a2";
}
.wd-icon-discount-filled.data-v-d4a8410a:before {
  content: "\e6a3";
}
.wd-icon-dashboard.data-v-d4a8410a:before {
  content: "\e6a4";
}
.wd-icon-discount.data-v-d4a8410a:before {
  content: "\e6a5";
}
.wd-icon-heart-filled.data-v-d4a8410a:before {
  content: "\e6a6";
}
.wd-icon-chat1.data-v-d4a8410a:before {
  content: "\e6a7";
}
.wd-icon-a-controlplatform.data-v-d4a8410a:before {
  content: "\e6a8";
}
.wd-icon-gift.data-v-d4a8410a:before {
  content: "\e6a9";
}
.wd-icon-photo.data-v-d4a8410a:before {
  content: "\e692";
}
.wd-icon-play-circle-stroke.data-v-d4a8410a:before {
  content: "\e693";
}
.wd-icon-notification.data-v-d4a8410a:before {
  content: "\e694";
}
.wd-icon-cloud.data-v-d4a8410a:before {
  content: "\e695";
}
.wd-icon-gender-female.data-v-d4a8410a:before {
  content: "\e696";
}
.wd-icon-fork.data-v-d4a8410a:before {
  content: "\e697";
}
.wd-icon-layers.data-v-d4a8410a:before {
  content: "\e698";
}
.wd-icon-lock-off.data-v-d4a8410a:before {
  content: "\e699";
}
.wd-icon-location.data-v-d4a8410a:before {
  content: "\e68a";
}
.wd-icon-mobile.data-v-d4a8410a:before {
  content: "\e68b";
}
.wd-icon-qrcode.data-v-d4a8410a:before {
  content: "\e68c";
}
.wd-icon-home1.data-v-d4a8410a:before {
  content: "\e68d";
}
.wd-icon-time.data-v-d4a8410a:before {
  content: "\e68e";
}
.wd-icon-heart.data-v-d4a8410a:before {
  content: "\e68f";
}
.wd-icon-lock-on.data-v-d4a8410a:before {
  content: "\e690";
}
.wd-icon-print.data-v-d4a8410a:before {
  content: "\e691";
}
.wd-icon-slash.data-v-d4a8410a:before {
  content: "\e67a";
}
.wd-icon-usb.data-v-d4a8410a:before {
  content: "\e67b";
}
.wd-icon-tools.data-v-d4a8410a:before {
  content: "\e67c";
}
.wd-icon-wifi.data-v-d4a8410a:before {
  content: "\e67d";
}
.wd-icon-star-filled.data-v-d4a8410a:before {
  content: "\e67e";
}
.wd-icon-server.data-v-d4a8410a:before {
  content: "\e67f";
}
.wd-icon-sound.data-v-d4a8410a:before {
  content: "\e680";
}
.wd-icon-a-precisemonitor.data-v-d4a8410a:before {
  content: "\e681";
}
.wd-icon-service.data-v-d4a8410a:before {
  content: "\e682";
}
.wd-icon-tips.data-v-d4a8410a:before {
  content: "\e683";
}
.wd-icon-pin.data-v-d4a8410a:before {
  content: "\e684";
}
.wd-icon-secured.data-v-d4a8410a:before {
  content: "\e685";
}
.wd-icon-star.data-v-d4a8410a:before {
  content: "\e686";
}
.wd-icon-gender-male.data-v-d4a8410a:before {
  content: "\e687";
}
.wd-icon-shop.data-v-d4a8410a:before {
  content: "\e688";
}
.wd-icon-money-circle.data-v-d4a8410a:before {
  content: "\e689";
}
.wd-icon-file-word.data-v-d4a8410a:before {
  content: "\e66a";
}
.wd-icon-file-unknown.data-v-d4a8410a:before {
  content: "\e66b";
}
.wd-icon-folder-open.data-v-d4a8410a:before {
  content: "\e66c";
}
.wd-icon-file-pdf.data-v-d4a8410a:before {
  content: "\e66d";
}
.wd-icon-folder.data-v-d4a8410a:before {
  content: "\e66e";
}
.wd-icon-folder-add.data-v-d4a8410a:before {
  content: "\e66f";
}
.wd-icon-file.data-v-d4a8410a:before {
  content: "\e670";
}
.wd-icon-file-image.data-v-d4a8410a:before {
  content: "\e671";
}
.wd-icon-file-powerpoint.data-v-d4a8410a:before {
  content: "\e672";
}
.wd-icon-file-add.data-v-d4a8410a:before {
  content: "\e673";
}
.wd-icon-file-icon.data-v-d4a8410a:before {
  content: "\e674";
}
.wd-icon-file-paste.data-v-d4a8410a:before {
  content: "\e675";
}
.wd-icon-file-excel.data-v-d4a8410a:before {
  content: "\e676";
}
.wd-icon-file-copy.data-v-d4a8410a:before {
  content: "\e677";
}
.wd-icon-video1.data-v-d4a8410a:before {
  content: "\e678";
}
.wd-icon-wallet.data-v-d4a8410a:before {
  content: "\e679";
}
.wd-icon-ie.data-v-d4a8410a:before {
  content: "\e65d";
}
.wd-icon-logo-codepen.data-v-d4a8410a:before {
  content: "\e65e";
}
.wd-icon-github-filled.data-v-d4a8410a:before {
  content: "\e65f";
}
.wd-icon-ie-filled.data-v-d4a8410a:before {
  content: "\e660";
}
.wd-icon-apple.data-v-d4a8410a:before {
  content: "\e661";
}
.wd-icon-windows-filled.data-v-d4a8410a:before {
  content: "\e662";
}
.wd-icon-internet.data-v-d4a8410a:before {
  content: "\e663";
}
.wd-icon-github.data-v-d4a8410a:before {
  content: "\e664";
}
.wd-icon-windows.data-v-d4a8410a:before {
  content: "\e665";
}
.wd-icon-apple-filled.data-v-d4a8410a:before {
  content: "\e666";
}
.wd-icon-chrome-filled.data-v-d4a8410a:before {
  content: "\e667";
}
.wd-icon-chrome.data-v-d4a8410a:before {
  content: "\e668";
}
.wd-icon-android.data-v-d4a8410a:before {
  content: "\e669";
}
.wd-icon-edit-outline.data-v-d4a8410a:before {
  content: "\e64a";
}
.wd-icon-detection.data-v-d4a8410a:before {
  content: "\e64b";
}
.wd-icon-check-outline.data-v-d4a8410a:before {
  content: "\e64c";
}
.wd-icon-close.data-v-d4a8410a:before {
  content: "\e64d";
}
.wd-icon-check.data-v-d4a8410a:before {
  content: "\e64e";
}
.wd-icon-arrow-left.data-v-d4a8410a:before {
  content: "\e64f";
}
.wd-icon-computer.data-v-d4a8410a:before {
  content: "\e650";
}
.wd-icon-clock.data-v-d4a8410a:before {
  content: "\e651";
}
.wd-icon-check-bold.data-v-d4a8410a:before {
  content: "\e652";
}
.wd-icon-bags.data-v-d4a8410a:before {
  content: "\e653";
}
.wd-icon-arrow-down.data-v-d4a8410a:before {
  content: "\e654";
}
.wd-icon-arrow-right.data-v-d4a8410a:before {
  content: "\e655";
}
.wd-icon-circle.data-v-d4a8410a:before {
  content: "\e656";
}
.wd-icon-arrow-thin-down.data-v-d4a8410a:before {
  content: "\e657";
}
.wd-icon-camera.data-v-d4a8410a:before {
  content: "\e658";
}
.wd-icon-close-bold.data-v-d4a8410a:before {
  content: "\e659";
}
.wd-icon-add-circle.data-v-d4a8410a:before {
  content: "\e65a";
}
.wd-icon-arrow-thin-up.data-v-d4a8410a:before {
  content: "\e65b";
}
.wd-icon-add.data-v-d4a8410a:before {
  content: "\e65c";
}
.wd-icon-keyboard-delete.data-v-d4a8410a:before {
  content: "\e634";
}
.wd-icon-transfer.data-v-d4a8410a:before {
  content: "\e635";
}
.wd-icon-eye-close.data-v-d4a8410a:before {
  content: "\e61f";
}
.wd-icon-delete.data-v-d4a8410a:before {
  content: "\e61e";
}
.wd-icon-download.data-v-d4a8410a:before {
  content: "\e636";
}
.wd-icon-picture.data-v-d4a8410a:before {
  content: "\e637";
}
.wd-icon-refresh.data-v-d4a8410a:before {
  content: "\e638";
}
.wd-icon-read.data-v-d4a8410a:before {
  content: "\e639";
}
.wd-icon-note.data-v-d4a8410a:before {
  content: "\e63a";
}
.wd-icon-phone.data-v-d4a8410a:before {
  content: "\e63b";
}
.wd-icon-lenovo.data-v-d4a8410a:before {
  content: "\e63c";
}
.wd-icon-home.data-v-d4a8410a:before {
  content: "\e63d";
}
.wd-icon-search.data-v-d4a8410a:before {
  content: "\e63e";
}
.wd-icon-fill-camera.data-v-d4a8410a:before {
  content: "\e63f";
}
.wd-icon-fill-arrow-down.data-v-d4a8410a:before {
  content: "\e640";
}
.wd-icon-arrow-up.data-v-d4a8410a:before {
  content: "\e61d";
}
.wd-icon-delete-thin.data-v-d4a8410a:before {
  content: "\e641";
}
.wd-icon-filter.data-v-d4a8410a:before {
  content: "\e642";
}
.wd-icon-evaluation.data-v-d4a8410a:before {
  content: "\e643";
}
.wd-icon-close-outline.data-v-d4a8410a:before {
  content: "\e644";
}
.wd-icon-dong.data-v-d4a8410a:before {
  content: "\e645";
}
.wd-icon-error-fill.data-v-d4a8410a:before {
  content: "\e646";
}
.wd-icon-chat.data-v-d4a8410a:before {
  content: "\e647";
}
.wd-icon-decrease.data-v-d4a8410a:before {
  content: "\e648";
}
.wd-icon-copy.data-v-d4a8410a:before {
  content: "\e649";
}
.wd-icon-setting.data-v-d4a8410a:before {
  content: "\e621";
}
.wd-icon-subscribe.data-v-d4a8410a:before {
  content: "\e622";
}
.wd-icon-jdm.data-v-d4a8410a:before {
  content: "\e620";
}
.wd-icon-spool.data-v-d4a8410a:before {
  content: "\e623";
}
.wd-icon-warning.data-v-d4a8410a:before {
  content: "\e624";
}
.wd-icon-wifi-error.data-v-d4a8410a:before {
  content: "\e625";
}
.wd-icon-star-on.data-v-d4a8410a:before {
  content: "\e626";
}
.wd-icon-rotate.data-v-d4a8410a:before {
  content: "\e627";
}
.wd-icon-translate-bold.data-v-d4a8410a:before {
  content: "\e628";
}
.wd-icon-keyboard-collapse.data-v-d4a8410a:before {
  content: "\e629";
}
.wd-icon-keywords.data-v-d4a8410a:before {
  content: "\e62a";
}
.wd-icon-scan.data-v-d4a8410a:before {
  content: "\e62b";
}
.wd-icon-view.data-v-d4a8410a:before {
  content: "\e62c";
}
.wd-icon-phone-compute.data-v-d4a8410a:before {
  content: "\e62d";
}
.wd-icon-video.data-v-d4a8410a:before {
  content: "\e62e";
}
.wd-icon-thin-arrow-left.data-v-d4a8410a:before {
  content: "\e62f";
}
.wd-icon-goods.data-v-d4a8410a:before {
  content: "\e630";
}
.wd-icon-list.data-v-d4a8410a:before {
  content: "\e631";
}
.wd-icon-warn-bold.data-v-d4a8410a:before {
  content: "\e632";
}
.wd-icon-more.data-v-d4a8410a:before {
  content: "\e633";
}