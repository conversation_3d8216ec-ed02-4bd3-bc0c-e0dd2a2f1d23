"use strict";
const common_vendor = require("../../common/vendor.js");
const __unplugin_components_0 = () => "../../components/fg-navbar/fg-navbar.js";
if (!Array) {
  const _easycom_fg_navbar2 = __unplugin_components_0;
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_fg_navbar2 + _component_layout_default_uni)();
}
const _easycom_fg_navbar = () => "../../components/fg-navbar/fg-navbar.js";
if (!Math) {
  (_easycom_fg_navbar + RequestComp + UploadComp)();
}
const RequestComp = () => "./components/request.js";
const UploadComp = () => "./components/upload.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "about",
  setup(__props) {
    const { safeAreaInsets } = common_vendor.index.getSystemInfoSync();
    return (_ctx, _cache) => {
      var _a;
      return {
        a: ((_a = common_vendor.unref(safeAreaInsets)) == null ? void 0 : _a.top) + "px"
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-b5177f87"]]);
wx.createPage(MiniProgramPage);
