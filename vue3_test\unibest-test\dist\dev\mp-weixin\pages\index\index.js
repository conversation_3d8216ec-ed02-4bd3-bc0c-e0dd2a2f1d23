"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const utils_platform = require("../../utils/platform.js");
if (!Array) {
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  _component_layout_default_uni();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "Home"
}), {
  __name: "index",
  setup(__props) {
    const { safeAreaInsets } = common_vendor.index.getSystemInfoSync();
    const author = common_vendor.ref("菲鸽");
    const description = common_vendor.ref(
      "unibest 是一个集成了多种工具和技术的 uniapp 开发模板，由 uniapp + Vue3 + Ts + Vite6 + UnoCss + VSCode 构建，模板具有代码提示、自动格式化、统一配置、代码片段等功能，并内置了许多常用的基本组件和基本功能，让你编写 uniapp 拥有 best 体验。"
    );
    common_vendor.onLoad(() => {
      console.log("项目作者:", author.value);
    });
    const getLocation = () => {
      common_vendor.index.authorize({
        scope: "scope.userLocation",
        success() {
          common_vendor.index.getLocation({
            type: "wgs84",
            success(res) {
              const latitude = res.latitude;
              const longitude = res.longitude;
              const speed = res.speed;
              const accuracy = res.accuracy;
              console.log("位置信息:", { latitude, longitude, speed, accuracy });
            },
            fail(err) {
              console.error("获取位置失败:", err);
            }
          });
        },
        fail() {
          console.log("用户拒绝授权位置信息");
        }
      });
    };
    return (_ctx, _cache) => {
      var _a;
      return {
        a: common_assets._imports_0,
        b: common_vendor.t(common_vendor.unref(description)),
        c: common_vendor.t(common_vendor.unref(utils_platform.PLATFORM).platform),
        d: common_vendor.o(getLocation),
        e: ((_a = common_vendor.unref(safeAreaInsets)) == null ? void 0 : _a.top) + "px"
      };
    };
  }
}));
wx.createPage(_sfc_main);
