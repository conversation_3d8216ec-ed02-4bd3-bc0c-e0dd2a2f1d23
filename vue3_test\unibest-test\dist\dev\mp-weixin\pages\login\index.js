"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
const utils_platform = require("../../utils/platform.js");
const api_login = require("../../api/login.js");
const utils_toast = require("../../utils/toast.js");
const utils_index = require("../../utils/index.js");
if (!Array) {
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_checkbox2 = common_vendor.resolveComponent("wd-checkbox");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_input2 + _easycom_wd_icon2 + _easycom_wd_button2 + _easycom_wd_checkbox2 + _component_layout_default_uni)();
}
const _easycom_wd_input = () => "../../node-modules/wot-design-uni/components/wd-input/wd-input.js";
const _easycom_wd_icon = () => "../../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_checkbox = () => "../../node-modules/wot-design-uni/components/wd-checkbox/wd-checkbox.js";
if (!Math) {
  (_easycom_wd_input + _easycom_wd_icon + _easycom_wd_button + _easycom_wd_checkbox)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const redirectRoute = common_vendor.ref("");
    const appTitle = common_vendor.ref("unibest");
    const appLogo = common_vendor.ref("/static/logo.svg");
    const userStore = store_user.useUserStore();
    const captcha = common_vendor.ref({
      captchaEnabled: false,
      uuid: "",
      image: ""
    });
    const loginForm = common_vendor.ref({
      username: "admin",
      password: "123456",
      code: "",
      uuid: ""
    });
    const agreePrivacy = common_vendor.ref(true);
    common_vendor.onLoad((option) => {
      captcha.value.captchaEnabled && refreshCaptcha();
      if (option.redirect) {
        redirectRoute.value = option.redirect;
      }
    });
    const handleAccountLogin = () => __async(null, null, function* () {
      if (!agreePrivacy.value) {
        utils_toast.toast.error("请阅读同意协议");
        return;
      }
      if (!loginForm.value.username) {
        utils_toast.toast.error("请输入用户名");
        return;
      }
      if (!loginForm.value.password) {
        utils_toast.toast.error("请输入密码");
        return;
      }
      if (captcha.value.captchaEnabled && !loginForm.value.code) {
        utils_toast.toast.error("请输入验证码");
        return;
      }
      yield userStore.login(loginForm.value);
      const targetUrl = redirectRoute.value || "/pages/index/index";
      if (utils_index.isTableBar(targetUrl)) {
        common_vendor.index.switchTab({ url: targetUrl });
      } else {
        common_vendor.index.redirectTo({ url: targetUrl });
      }
    });
    const handleWechatLogin = () => __async(null, null, function* () {
      if (!utils_platform.isMpWeixin) {
        utils_toast.toast.info("请在微信小程序中使用此功能");
        return;
      }
      if (!agreePrivacy.value) {
        utils_toast.toast.error("请先阅读并同意用户协议和隐私政策");
        return;
      }
      yield userStore.wxLogin();
      const targetUrl = redirectRoute.value || "/pages/index/index";
      if (utils_index.isTableBar(targetUrl)) {
        common_vendor.index.switchTab({ url: targetUrl });
      } else {
        common_vendor.index.redirectTo({ url: targetUrl });
      }
    });
    const refreshCaptcha = () => {
      api_login.getCode().then((res) => {
        const { data } = res;
        loginForm.value.uuid = data.uuid;
        captcha.value = data;
      });
    };
    const handleAgreement = (type) => {
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: appLogo.value,
        b: common_vendor.t(appTitle.value),
        c: common_vendor.o(($event) => loginForm.value.username = $event),
        d: common_vendor.p({
          ["prefix-icon"]: "user",
          placeholder: "请输入用户名",
          clearable: true,
          border: false,
          required: true,
          modelValue: loginForm.value.username
        }),
        e: common_vendor.o(($event) => loginForm.value.password = $event),
        f: common_vendor.p({
          ["prefix-icon"]: "lock-on",
          placeholder: "请输入密码",
          clearable: true,
          ["show-password"]: true,
          border: false,
          required: true,
          modelValue: loginForm.value.password
        }),
        g: captcha.value.captchaEnabled
      }, captcha.value.captchaEnabled ? {
        h: "data:image/gif;base64," + captcha.value.image,
        i: common_vendor.o(refreshCaptcha),
        j: common_vendor.o(($event) => loginForm.value.code = $event),
        k: common_vendor.p({
          ["prefix-icon"]: "secured",
          placeholder: "请输入验证码",
          clearable: true,
          border: false,
          required: true,
          modelValue: loginForm.value.code
        })
      } : {}, {
        l: common_vendor.p({
          name: "right"
        }),
        m: common_vendor.o(handleAccountLogin),
        n: common_vendor.p({
          type: "primary",
          size: "large",
          block: true
        }),
        o: common_vendor.o(handleWechatLogin),
        p: common_vendor.p({
          type: "info",
          size: "large",
          block: true,
          plain: true
        }),
        q: common_vendor.o(($event) => handleAgreement()),
        r: common_vendor.o(($event) => handleAgreement()),
        s: common_vendor.o(($event) => agreePrivacy.value = $event),
        t: common_vendor.p({
          shape: "square",
          ["active-color"]: "var(--wot-color-theme, #1989fa)",
          modelValue: agreePrivacy.value
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-********"]]);
wx.createPage(MiniProgramPage);
