<layout-default-uni class="data-v-********" u-s="{{['d']}}" u-i="********-0" bind:__l="__l"><view class="login-container data-v-********"><view class="bg-decoration bg-circle-1 data-v-********"></view><view class="bg-decoration bg-circle-2 data-v-********"></view><view class="bg-decoration bg-circle-3 data-v-********"></view><view class="login-header data-v-********"><image class="login-logo data-v-********" src="{{a}}" mode="aspectFit"></image><view class="login-title data-v-********">{{b}}</view></view><view class="login-form data-v-********"><view class="welcome-text data-v-********">欢迎登录</view><view class="login-desc data-v-********">请输入您的账号和密码</view><view class="login-input-group data-v-********"><view class="input-wrapper data-v-********"><wd-input wx:if="{{d}}" class="login-input data-v-********" u-i="********-1,********-0" bind:__l="__l" bindupdateModelValue="{{c}}" u-p="{{d}}"></wd-input><view class="input-bottom-line data-v-********"></view></view><view class="input-wrapper data-v-********"><wd-input wx:if="{{f}}" class="login-input data-v-********" u-i="********-2,********-0" bind:__l="__l" bindupdateModelValue="{{e}}" u-p="{{f}}"></wd-input><view class="input-bottom-line data-v-********"></view></view><view class="input-wrapper captcha-wrapper data-v-********"><wd-input wx:if="{{g}}" u-s="{{['suffix']}}" class="login-input captcha-input data-v-********" u-i="********-3,********-0" bind:__l="__l" bindupdateModelValue="{{j}}" u-p="{{k}}"><image class="captcha-image data-v-********" src="{{h}}" mode="aspectFit" bindtap="{{i}}" slot="suffix"></image></wd-input><view class="input-bottom-line data-v-********"></view></view></view><view class="login-buttons data-v-********"><wd-button wx:if="{{n}}" u-s="{{['d']}}" bindclick="{{m}}" class="account-login-btn data-v-********" u-i="********-4,********-0" bind:__l="__l" u-p="{{n}}"><wd-icon wx:if="{{l}}" class="login-icon size-18px data-v-********" u-i="********-5,********-4" bind:__l="__l" u-p="{{l}}"></wd-icon> 登录 </wd-button><view class="divider data-v-********"><view class="divider-line data-v-********"></view><view class="divider-text data-v-********">或</view><view class="divider-line data-v-********"></view></view><wd-button wx:if="{{p}}" u-s="{{['d']}}" bindclick="{{o}}" class="wechat-login-btn data-v-********" u-i="********-6,********-0" bind:__l="__l" u-p="{{p}}"> 微信一键登录 </wd-button></view></view><view class="privacy-agreement data-v-********"><wd-checkbox wx:if="{{t}}" u-s="{{['d']}}" class="privacy-checkbox data-v-********" u-i="********-7,********-0" bind:__l="__l" bindupdateModelValue="{{s}}" u-p="{{t}}"><view class="agreement-text data-v-********"> 我已阅读并同意 <text class="agreement-link data-v-********" catchtap="{{q}}">《用户协议》</text> 和 <text class="agreement-link data-v-********" catchtap="{{r}}">《隐私政策》</text></view></wd-checkbox></view><view class="login-footer data-v-********"></view></view></layout-default-uni>