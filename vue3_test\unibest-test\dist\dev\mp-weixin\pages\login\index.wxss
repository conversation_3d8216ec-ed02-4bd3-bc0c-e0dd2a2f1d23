/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 验证码输入框样式 */
.captcha-wrapper .captcha-input.data-v-******** .wd-input__suffix {
  margin-right: 0;
  padding-right: 0;
}
.captcha-wrapper .captcha-image.data-v-******** {
  width: 100px;
  height: 36px;
  margin-left: 10px;
  border-radius: 8px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.captcha-wrapper .captcha-image.data-v-********::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), transparent);
  pointer-events: none;
}
.captcha-wrapper .captcha-image.data-v-********:active {
  opacity: 0.8;
  transform: scale(0.96);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}
.login-container.data-v-******** {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 0 70rpx;
  background-color: #ffffff;
  background-image: linear-gradient(135deg, rgba(25, 137, 250, 0.05) 0%, rgba(255, 255, 255, 0) 100%);
  position: relative;
  overflow: hidden;
}

/* 背景装饰元素 */
.bg-decoration.data-v-******** {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(25, 137, 250, 0.05), rgba(25, 137, 250, 0.1));
  z-index: 0;
  pointer-events: none;
}
.bg-circle-1.data-v-******** {
  width: 500rpx;
  height: 500rpx;
  top: -200rpx;
  right: -200rpx;
  opacity: 0.6;
}
.bg-circle-2.data-v-******** {
  width: 400rpx;
  height: 400rpx;
  bottom: 10%;
  left: -200rpx;
  opacity: 0.4;
}
.bg-circle-3.data-v-******** {
  width: 300rpx;
  height: 300rpx;
  bottom: -100rpx;
  right: 10%;
  opacity: 0.3;
  background: linear-gradient(135deg, rgba(7, 193, 96, 0.05), rgba(7, 193, 96, 0.1));
}
.login-header.data-v-******** {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 120rpx;
  animation: fadeInDown-******** 0.8s ease-out;
}
.login-header .login-logo.data-v-******** {
  width: 200rpx;
  height: 200rpx;
  border-radius: 36rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.12);
  transition: all 0.3s ease;
}
.login-header .login-logo.data-v-********:active {
  transform: scale(0.95);
  box-shadow: 0 6rpx 15rpx rgba(0, 0, 0, 0.1);
}
.login-header .login-title.data-v-******** {
  margin-top: 30rpx;
  font-size: 46rpx;
  font-weight: bold;
  color: #333333;
  letter-spacing: 3rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}
.login-form.data-v-******** {
  flex: 1;
  margin-top: 70rpx;
  animation: fadeIn-******** 0.8s ease-out 0.2s both;
}
.login-form .welcome-text.data-v-******** {
  margin-bottom: 16rpx;
  font-size: 48rpx;
  font-weight: bold;
  color: #333333;
  text-align: center;
  letter-spacing: 1rpx;
}
.login-form .login-desc.data-v-******** {
  margin-bottom: 70rpx;
  font-size: 28rpx;
  color: #888888;
  text-align: center;
}
.login-form .login-input-group.data-v-******** {
  margin-bottom: 60rpx;
  position: relative;
  z-index: 1;
}
.login-form .login-input-group .input-wrapper.data-v-******** {
  position: relative;
  margin-bottom: 50rpx;
  transition: all 0.3s ease;
  border-radius: 16rpx;
  overflow: hidden;
}
.login-form .login-input-group .input-wrapper.data-v-********:last-child {
  margin-bottom: 0;
}
.login-form .login-input-group .input-wrapper .login-input.data-v-******** {
  padding: 12rpx 20rpx;
  background-color: rgba(245, 247, 250, 0.7);
  border-radius: 16rpx;
  transition: all 0.3s ease;
}
.login-form .login-input-group .input-wrapper .login-input.data-v-******** .wd-input__inner {
  font-size: 30rpx;
  color: #333333;
}
.login-form .login-input-group .input-wrapper .login-input.data-v-******** .wd-input__placeholder {
  font-size: 28rpx;
  color: #aaaaaa;
}
.login-form .login-input-group .input-wrapper .login-input.data-v-********:focus-within {
  background-color: rgba(245, 247, 250, 0.95);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.06);
  transform: translateY(-3rpx);
}
.login-form .login-input-group .input-wrapper .input-bottom-line.data-v-******** {
  position: absolute;
  bottom: -2rpx;
  left: 5%;
  width: 90%;
  height: 2rpx;
  background: linear-gradient(to right, transparent, var(--wot-color-theme, #1989fa), transparent);
  transition: transform 0.4s ease;
  transform: scaleX(0);
  opacity: 0.8;
}
.login-form .login-input-group .input-wrapper:focus-within .input-bottom-line.data-v-******** {
  transform: scaleX(1);
}
.login-form .login-input-group .input-wrapper .input-icon.data-v-******** {
  margin-right: 16rpx;
  color: #666666;
  transition: color 0.3s ease;
}
.login-form .login-input-group .input-wrapper:focus-within .input-icon.data-v-******** {
  color: var(--wot-color-theme, #1989fa);
}
.login-form .login-buttons.data-v-******** {
  display: flex;
  flex-direction: column;
  gap: 36rpx;
}
.login-form .login-buttons .account-login-btn.data-v-******** {
  height: 96rpx;
  margin-top: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  letter-spacing: 2rpx;
  border-radius: 48rpx;
  box-shadow: 0 10rpx 20rpx rgba(25, 137, 250, 0.25);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}
.login-form .login-buttons .account-login-btn .login-icon.data-v-******** {
  margin-right: 8rpx;
  opacity: 0.8;
  transition: all 0.3s ease;
}
.login-form .login-buttons .account-login-btn.data-v-********:active {
  box-shadow: 0 5rpx 10rpx rgba(25, 137, 250, 0.2);
  transform: scale(0.98);
}
.login-form .login-buttons .account-login-btn:active .login-icon.data-v-******** {
  transform: translateX(3rpx);
}
.login-form .login-buttons .divider.data-v-******** {
  display: flex;
  align-items: center;
  margin: 24rpx 0;
}
.login-form .login-buttons .divider .divider-line.data-v-******** {
  flex: 1;
  height: 1px;
  background-color: #eeeeee;
}
.login-form .login-buttons .divider .divider-text.data-v-******** {
  padding: 0 24rpx;
  font-size: 24rpx;
  color: #999999;
}
.login-form .login-buttons .wechat-login-btn.data-v-******** {
  height: 96rpx;
  font-size: 32rpx;
  color: #07c160;
  border-color: #07c160;
  border-radius: 48rpx;
  transition: all 0.3s ease;
}
.login-form .login-buttons .wechat-login-btn .wechat-icon.data-v-******** {
  margin-right: 12rpx;
}
.login-form .login-buttons .wechat-login-btn.data-v-********:active {
  background-color: rgba(7, 193, 96, 0.08);
  transform: scale(0.98);
}
.privacy-agreement.data-v-******** {
  display: flex;
  justify-content: center;
  margin: 30rpx 0 40rpx;
  animation: fadeIn-******** 0.8s ease-out 0.4s both;
}
.privacy-agreement .privacy-checkbox.data-v-******** {
  display: flex;
  align-items: center;
}
.privacy-agreement .agreement-text.data-v-******** {
  font-size: 26rpx;
  line-height: 1.6;
  color: #666666;
}
.privacy-agreement .agreement-text .agreement-link.data-v-******** {
  padding: 0 4rpx;
  font-weight: 500;
  color: var(--wot-color-theme, #1989fa);
  transition: all 0.3s ease;
}
.privacy-agreement .agreement-text .agreement-link.data-v-********:active {
  opacity: 0.8;
  transform: scale(0.98);
}
.login-footer.data-v-******** {
  padding: 50rpx 0;
  margin-top: auto;
}

/* 添加动画效果 */
@keyframes fadeIn-******** {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@keyframes fadeInDown-******** {
from {
    opacity: 0;
    transform: translateY(-20px);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}