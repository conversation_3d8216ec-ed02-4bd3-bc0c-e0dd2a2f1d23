/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.about-container.data-v-51b18cf8 {
  background-color: #f5f7fa;
  padding: 30rpx;
}
.about-card.data-v-51b18cf8 {
  background-color: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
  padding: 40rpx 30rpx;
}

/* 应用信息 */
.app-info.data-v-51b18cf8 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 0 50rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.logo-wrapper.data-v-51b18cf8 {
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.08);
  border-radius: 24rpx;
}
.app-name.data-v-51b18cf8 {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}
.app-version.data-v-51b18cf8 {
  font-size: 28rpx;
  color: #999;
}

/* 信息区块 */
.info-section.data-v-51b18cf8 {
  padding: 40rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}
.section-title.data-v-51b18cf8 {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 24rpx;
}
.section-title.data-v-51b18cf8::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #4a7bff, #6a5acd);
  border-radius: 4rpx;
}
.section-content.data-v-51b18cf8 {
  padding: 0 10rpx;
}
.content-text.data-v-51b18cf8 {
  font-size: 30rpx;
  color: #666;
  line-height: 1.6;
  text-align: justify;
}

/* 联系方式 */
.contact-item.data-v-51b18cf8 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.contact-item.data-v-51b18cf8:last-child {
  margin-bottom: 0;
}
.contact-icon.data-v-51b18cf8 {
  margin-right: 20rpx;
  color: #4a7bff;
}
.contact-text.data-v-51b18cf8 {
  font-size: 30rpx;
  color: #666;
}

/* 版权信息 */
.copyright.data-v-51b18cf8 {
  padding-top: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.copyright text.data-v-51b18cf8 {
  font-size: 26rpx;
  color: #999;
  line-height: 1.6;
}