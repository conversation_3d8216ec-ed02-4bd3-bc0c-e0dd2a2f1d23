"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
require("../../store/index.js");
const utils_uploadFile = require("../../utils/uploadFile.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _easycom_wd_img2 = common_vendor.resolveComponent("wd-img");
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  const _easycom_wd_cell2 = common_vendor.resolveComponent("wd-cell");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_img2 + _easycom_wd_icon2 + _easycom_wd_cell2 + _easycom_wd_button2 + _component_layout_default_uni)();
}
const _easycom_wd_img = () => "../../node-modules/wot-design-uni/components/wd-img/wd-img.js";
const _easycom_wd_icon = () => "../../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
const _easycom_wd_cell = () => "../../node-modules/wot-design-uni/components/wd-cell/wd-cell.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
if (!Math) {
  (_easycom_wd_img + _easycom_wd_icon + _easycom_wd_cell + _easycom_wd_button)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const toast = common_vendor.useToast();
    const hasLogin = common_vendor.ref(false);
    common_vendor.onShow((options) => {
      hasLogin.value = !!common_vendor.index.getStorageSync("token");
      console.log("个人中心onShow", hasLogin.value, options);
      hasLogin.value && store_user.useUserStore().getUserInfo();
    });
    const handleLogin = () => __async(null, null, function* () {
      yield userStore.wxLogin();
      hasLogin.value = true;
    });
    const onChooseAvatar = (e) => {
      console.log("选择头像", e.detail);
      const { avatarUrl } = e.detail;
      const { run } = utils_uploadFile.useUpload(
        utils_uploadFile.uploadFileUrl.USER_AVATAR,
        {},
        {
          onSuccess: (res) => store_user.useUserStore().getUserInfo()
        },
        avatarUrl
      );
      run();
    };
    const handleProfileInfo = () => {
      common_vendor.index.navigateTo({ url: `/pages/mine/info/index` });
    };
    const handlePassword = () => {
      common_vendor.index.navigateTo({ url: `/pages/mine/password/index` });
    };
    const handleInform = () => {
      toast.success("功能开发中");
    };
    const handleAppUpdate = () => {
      const updateManager = common_vendor.index.getUpdateManager();
      updateManager.onCheckForUpdate(function(res) {
        if (res.hasUpdate) {
          toast.success("检测到新版本，正在下载中...");
        } else {
          toast.success("已是最新版本");
        }
      });
      updateManager.onUpdateReady(function(res) {
        common_vendor.index.showModal({
          title: "更新提示",
          content: "新版本已经准备好，是否重启应用？",
          success(res2) {
            if (res2.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });
      updateManager.onUpdateFailed(function(res) {
        toast.error("新版本下载失败");
      });
    };
    const handleAbout = () => {
      common_vendor.index.navigateTo({ url: `/pages/mine/about/index` });
    };
    const handleClearCache = () => {
      common_vendor.index.showModal({
        title: "清除缓存",
        content: "确定要清除所有缓存吗？\n清除后需要重新登录",
        success: (res) => {
          if (res.confirm) {
            try {
              common_vendor.index.clearStorageSync();
              store_user.useUserStore().logout();
              toast.success("清除缓存成功");
            } catch (err) {
              console.error("清除缓存失败:", err);
              toast.error("清除缓存失败");
            }
          }
        }
      });
    };
    const handleLogout = () => {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要退出登录吗？",
        success: (res) => {
          if (res.confirm) {
            store_user.useUserStore().logout();
            hasLogin.value = false;
            toast.success("退出登录成功");
          }
        }
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(JSON.stringify(common_vendor.unref(userStore).userInfo)),
        b: common_vendor.p({
          src: common_vendor.unref(userStore).userInfo.avatar,
          width: "80px",
          height: "80px",
          radius: "50%"
        }),
        c: common_vendor.o(onChooseAvatar),
        d: common_vendor.unref(userStore).userInfo.username,
        e: common_vendor.o(($event) => common_vendor.unref(userStore).userInfo.username = $event.detail.value),
        f: common_vendor.t(common_vendor.unref(userStore).userInfo.id),
        g: common_vendor.p({
          name: "user"
        }),
        h: common_vendor.o(handleProfileInfo),
        i: common_vendor.p({
          title: "个人资料",
          ["is-link"]: true
        }),
        j: common_vendor.p({
          name: "lock-on"
        }),
        k: common_vendor.o(handlePassword),
        l: common_vendor.p({
          title: "账号安全",
          ["is-link"]: true
        }),
        m: common_vendor.p({
          name: "notification"
        }),
        n: common_vendor.o(handleInform),
        o: common_vendor.p({
          title: "消息通知",
          ["is-link"]: true
        }),
        p: common_vendor.p({
          name: "clear"
        }),
        q: common_vendor.o(handleClearCache),
        r: common_vendor.p({
          title: "清理缓存",
          ["is-link"]: true
        }),
        s: common_vendor.p({
          name: "refresh1"
        }),
        t: common_vendor.o(handleAppUpdate),
        v: common_vendor.p({
          title: "应用更新",
          ["is-link"]: true
        }),
        w: common_vendor.p({
          name: "info-circle"
        }),
        x: common_vendor.o(handleAbout),
        y: common_vendor.p({
          title: "关于我们",
          ["is-link"]: true
        }),
        z: common_vendor.unref(hasLogin)
      }, common_vendor.unref(hasLogin) ? {
        A: common_vendor.o(handleLogout),
        B: common_vendor.p({
          type: "error",
          block: true
        })
      } : {
        C: common_vendor.o(handleLogin),
        D: common_vendor.p({
          type: "primary",
          block: true
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-9023ef44"]]);
wx.createPage(MiniProgramPage);
