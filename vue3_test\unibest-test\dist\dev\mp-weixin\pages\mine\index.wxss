/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 基础样式 */
.profile-container.data-v-9023ef44 {
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", sans-serif;
  background-color: #f7f8fa;
}

/* 用户信息区域 */
.user-info-section.data-v-9023ef44 {
  display: flex;
  align-items: center;
  padding: 40rpx;
  margin: 30rpx 30rpx 20rpx;
  background-color: #fff;
  border-radius: 24rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}
.avatar-wrapper.data-v-9023ef44 {
  width: 160rpx;
  height: 160rpx;
  margin-right: 40rpx;
  overflow: hidden;
  border: 4rpx solid #f5f5f5;
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}
.avatar-button.data-v-9023ef44 {
  height: 160rpx;
  padding: 0;
  margin-right: 40rpx;
  overflow: hidden;
  border: 4rpx solid #f5f5f5;
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}
.user-details.data-v-9023ef44 {
  flex: 1;
}
.username.data-v-9023ef44 {
  margin-bottom: 12rpx;
  font-size: 38rpx;
  font-weight: 600;
  color: #333;
  letter-spacing: 0.5rpx;
}
.user-id.data-v-9023ef44 {
  font-size: 28rpx;
  color: #666;
}
.user-created.data-v-9023ef44 {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #999;
}

/* 功能区块 */
.function-section.data-v-9023ef44 {
  padding: 0 20rpx;
  margin-top: 20rpx;
}
.cell-group.data-v-9023ef44 {
  margin-bottom: 20rpx;
  overflow: hidden;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.group-title.data-v-9023ef44 {
  padding: 24rpx 30rpx 16rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #999;
  background-color: #fafafa;
}
.data-v-9023ef44 .wd-cell {
  border-bottom: 1rpx solid #f5f5f5;
}
.data-v-9023ef44 .wd-cell:last-child {
  border-bottom: none;
}
.data-v-9023ef44 .wd-cell .wd-cell__title {
  margin-left: 5px;
  font-size: 32rpx;
  color: #333;
}
.data-v-9023ef44 .wd-cell .cell-icon {
  margin-right: 20rpx;
  font-size: 36rpx;
}

/* 退出登录按钮 */
.logout-button-wrapper.data-v-9023ef44 {
  padding: 40rpx 30rpx;
}
.data-v-9023ef44 .wd-button--danger {
  height: 88rpx;
  font-size: 32rpx;
  line-height: 88rpx;
  color: #fff;
  background-color: #f53f3f;
  border-radius: 44rpx;
}