"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../common/vendor.js");
require("../../../store/index.js");
const utils_toast = require("../../../utils/toast.js");
const api_login = require("../../../api/login.js");
const store_user = require("../../../store/user.js");
if (!Array) {
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  const _easycom_wd_cell_group2 = common_vendor.resolveComponent("wd-cell-group");
  const _easycom_wd_form2 = common_vendor.resolveComponent("wd-form");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_input2 + _easycom_wd_cell_group2 + _easycom_wd_form2 + _easycom_wd_button2 + _component_layout_default_uni)();
}
const _easycom_wd_input = () => "../../../node-modules/wot-design-uni/components/wd-input/wd-input.js";
const _easycom_wd_cell_group = () => "../../../node-modules/wot-design-uni/components/wd-cell-group/wd-cell-group.js";
const _easycom_wd_form = () => "../../../node-modules/wot-design-uni/components/wd-form/wd-form.js";
const _easycom_wd_button = () => "../../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
if (!Math) {
  (_easycom_wd_input + _easycom_wd_cell_group + _easycom_wd_form + _easycom_wd_button)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const formRef = common_vendor.ref();
    const userStore = store_user.useUserStore();
    const { userInfo } = common_vendor.storeToRefs(userStore);
    const formData = common_vendor.ref({
      id: userInfo.value.id,
      oldPassword: "",
      newPassword: "",
      confirmPassword: ""
    });
    const handleSubmit = () => __async(null, null, function* () {
      const valid = yield formRef.value.validate();
      if (!valid) return;
      const { message } = yield api_login.updateUserPassword(formData.value);
      yield store_user.useUserStore().logout();
      utils_toast.toast.success("修改成功，请重新登录");
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => formData.value.oldPassword = $event),
        b: common_vendor.p({
          prop: "oldPassword",
          clearable: true,
          placeholder: "请输入旧密码",
          ["show-password"]: true,
          rules: [{
            required: true,
            message: "请填写旧密码"
          }],
          modelValue: formData.value.oldPassword
        }),
        c: common_vendor.o(($event) => formData.value.newPassword = $event),
        d: common_vendor.p({
          prop: "newPassword",
          clearable: true,
          placeholder: "请输入新密码",
          ["show-password"]: true,
          rules: [{
            required: true,
            message: "请填写新密码"
          }],
          modelValue: formData.value.newPassword
        }),
        e: common_vendor.o(($event) => formData.value.confirmPassword = $event),
        f: common_vendor.p({
          prop: "confirmPassword",
          clearable: true,
          placeholder: "请输入新密码",
          ["show-password"]: true,
          rules: [{
            required: true,
            message: "请填写新密码"
          }],
          modelValue: formData.value.confirmPassword
        }),
        g: common_vendor.sr(formRef, "cddca36c-1,cddca36c-0", {
          "k": "formRef"
        }),
        h: common_vendor.p({
          model: formData.value,
          ["label-width"]: "160rpx"
        }),
        i: common_vendor.o(handleSubmit),
        j: common_vendor.p({
          type: "primary",
          size: "large"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-cddca36c"]]);
wx.createPage(MiniProgramPage);
