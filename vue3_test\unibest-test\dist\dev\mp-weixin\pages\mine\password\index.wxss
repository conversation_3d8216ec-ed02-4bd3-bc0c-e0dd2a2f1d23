/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.profile-info-container.data-v-cddca36c {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 30rpx;
}
.profile-card.data-v-cddca36c {
  background-color: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
}
.card-header.data-v-cddca36c {
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.card-title.data-v-cddca36c {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  display: inline-block;
  padding-bottom: 16rpx;
}
.card-title.data-v-cddca36c::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #4a7bff, #6a5acd);
  border-radius: 6rpx;
}
.form-wrapper.data-v-cddca36c {
  padding: 30rpx;
}
.form-group.data-v-cddca36c {
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 40rpx;
}
.form-input.data-v-cddca36c {
  font-size: 30rpx;
}
.sex-field.data-v-cddca36c {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  background-color: #ffffff;
}
.field-label.data-v-cddca36c {
  width: 160rpx;
  font-size: 30rpx;
  color: #333;
}
.radio-group.data-v-cddca36c {
  flex: 1;
  display: flex;
  gap: 20rpx;
}
.radio-btn.data-v-cddca36c {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 30rpx;
  border-radius: 12rpx;
  background-color: #f5f7fa;
}
.radio-btn.data-v-cddca36c:active {
  opacity: 0.8;
}
.form-actions.data-v-cddca36c {
  display: flex;
  flex-direction: row;
  gap: 20rpx;
}
.submit-btn.data-v-cddca36c {
  height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
  font-weight: 500;
  background: linear-gradient(135deg, #4a7bff, #6a5acd);
  box-shadow: 0 8rpx 16rpx rgba(74, 123, 255, 0.2);
  transition: all 0.3s ease;
}
.submit-btn.data-v-cddca36c:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 8rpx rgba(74, 123, 255, 0.15);
}