"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../common/vendor.js");
const api_login = require("../api/login.js");
const utils_toast = require("../utils/toast.js");
const userInfoState = {
  id: 0,
  username: "",
  avatar: "/static/images/default-avatar.png",
  token: ""
};
const useUserStore = common_vendor.defineStore(
  "user",
  () => {
    const userInfo = common_vendor.ref(__spreadValues({}, userInfoState));
    const setUserInfo = (val) => {
      console.log("设置用户信息", val);
      if (!val.avatar) {
        val.avatar = userInfoState.avatar;
      } else {
        val.avatar = "https://oss.laf.run/ukw0y1-site/avatar.jpg?feige";
      }
      userInfo.value = val;
    };
    const removeUserInfo = () => {
      userInfo.value = __spreadValues({}, userInfoState);
      common_vendor.index.removeStorageSync("userInfo");
      common_vendor.index.removeStorageSync("token");
    };
    const login = (credentials) => __async(null, null, function* () {
      const res = yield api_login.login(credentials);
      console.log("登录信息", res);
      utils_toast.toast.success("登录成功");
      getUserInfo();
      return res;
    });
    const getUserInfo = () => __async(null, null, function* () {
      const res = yield api_login.getUserInfo();
      const userInfo2 = res.data;
      setUserInfo(userInfo2);
      common_vendor.index.setStorageSync("userInfo", userInfo2);
      common_vendor.index.setStorageSync("token", userInfo2.token);
      return res;
    });
    const logout = () => __async(null, null, function* () {
      api_login.logout();
      removeUserInfo();
    });
    const wxLogin = () => __async(null, null, function* () {
      const data = yield api_login.getWxCode();
      console.log("微信登录code", data);
      const res = yield api_login.wxLogin(data);
      getUserInfo();
      return res;
    });
    return {
      userInfo,
      login,
      wxLogin,
      getUserInfo,
      logout
    };
  },
  {
    persist: true
  }
);
exports.useUserStore = useUserStore;
