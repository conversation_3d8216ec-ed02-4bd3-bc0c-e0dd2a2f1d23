"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../common/vendor.js");
const utils_platform = require("./platform.js");
const globalStyle = { "navigationStyle": "default", "navigationBarTitleText": "unibest", "navigationBarBackgroundColor": "#f8f8f8", "navigationBarTextStyle": "black", "backgroundColor": "#FFFFFF" };
const easycom = { "autoscan": true, "custom": { "^fg-(.*)": "@/components/fg-$1/fg-$1.vue", "^wd-(.*)": "wot-design-uni/components/wd-$1/wd-$1.vue", "^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)": "z-paging/components/z-paging$1/z-paging$1.vue" } };
const tabBar$1 = { "color": "#999999", "selectedColor": "#018d71", "backgroundColor": "#F8F8F8", "borderStyle": "black", "height": "50px", "fontSize": "10px", "iconWidth": "24px", "spacing": "3px", "list": [{ "iconPath": "static/tabbar/home.png", "selectedIconPath": "static/tabbar/homeHL.png", "pagePath": "pages/index/index", "text": "首页" }, { "iconPath": "static/tabbar/example.png", "selectedIconPath": "static/tabbar/exampleHL.png", "pagePath": "pages/about/about", "text": "关于" }, { "iconPath": "static/tabbar/personal.png", "selectedIconPath": "static/tabbar/personalHL.png", "pagePath": "pages/mine/index", "text": "我的" }] };
const pages$1 = [{ "path": "pages/index/index", "type": "home", "style": { "navigationStyle": "custom", "navigationBarTitleText": "首页" } }, { "path": "pages/about/about", "type": "page", "style": { "navigationBarTitleText": "关于", "navigationStyle": "custom" } }, { "path": "pages/login/index", "type": "page", "style": { "navigationBarTitleText": "登录", "navigationStyle": "custom" } }, { "path": "pages/mine/index", "type": "page", "style": { "navigationBarTitleText": "我的" } }, { "path": "pages/test/index", "type": "page" }, { "path": "pages/mine/about/index", "type": "page", "style": { "navigationBarTitleText": "关于我们" } }, { "path": "pages/mine/info/index", "type": "page", "style": { "navigationBarTitleText": "个人资料" } }, { "path": "pages/mine/password/index", "type": "page", "style": { "navigationBarTitleText": "修改密码" } }];
const subPackages$1 = [];
const pagesConfig = {
  globalStyle,
  easycom,
  tabBar: tabBar$1,
  pages: pages$1,
  subPackages: subPackages$1
};
const { pages, subPackages, tabBar = { list: [] } } = __spreadValues({}, pagesConfig);
const getLastPage = () => {
  const pages2 = getCurrentPages();
  return pages2[pages2.length - 1];
};
(tabBar == null ? void 0 : tabBar.list) || [];
const isTableBar = (path) => {
  if (!tabBar) {
    return false;
  }
  if (!tabBar.list.length) {
    return false;
  }
  if (path.startsWith("/")) {
    path = path.substring(1);
  }
  return !!tabBar.list.find((e) => e.pagePath === path);
};
const getAllPages = (key = "needLogin") => {
  const mainPages = [
    ...pages.filter((page) => !key || page[key]).map((page) => __spreadProps(__spreadValues({}, page), {
      path: `/${page.path}`
    }))
  ];
  const subPages = [];
  subPackages.forEach((subPageObj) => {
    const { root } = subPageObj;
    subPageObj.pages.filter((page) => !key || page[key]).forEach((page) => {
      subPages.push(__spreadProps(__spreadValues({}, page), {
        path: `/${root}/${page.path}`
      }));
    });
  });
  const result = [...mainPages, ...subPages];
  return result;
};
const getNeedLoginPages = () => getAllPages("needLogin").map((page) => page.path);
getAllPages("needLogin").map((page) => page.path);
const getEnvBaseUrl = () => {
  let baseUrl = "https://ukw0y1.laf.run";
  if (utils_platform.isMpWeixin) {
    const {
      miniProgram: { envVersion }
    } = common_vendor.index.getAccountInfoSync();
    switch (envVersion) {
      case "develop":
        baseUrl = "https://ukw0y1.laf.run";
        break;
      case "trial":
        baseUrl = "https://ukw0y1.laf.run";
        break;
      case "release":
        baseUrl = "https://ukw0y1.laf.run";
        break;
    }
  }
  return baseUrl;
};
const getEnvBaseUploadUrl = () => {
  let baseUploadUrl = "https://ukw0y1.laf.run/upload";
  if (utils_platform.isMpWeixin) {
    const {
      miniProgram: { envVersion }
    } = common_vendor.index.getAccountInfoSync();
    switch (envVersion) {
      case "develop":
        baseUploadUrl = "https://ukw0y1.laf.run/upload";
        break;
      case "trial":
        baseUploadUrl = "https://ukw0y1.laf.run/upload";
        break;
      case "release":
        baseUploadUrl = "https://ukw0y1.laf.run/upload";
        break;
    }
  }
  return baseUploadUrl;
};
exports.getEnvBaseUploadUrl = getEnvBaseUploadUrl;
exports.getEnvBaseUrl = getEnvBaseUrl;
exports.getLastPage = getLastPage;
exports.getNeedLoginPages = getNeedLoginPages;
exports.isTableBar = isTableBar;
