/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    FgNavbar: typeof import('./../components/fg-navbar/fg-navbar.vue')['default']
    PrivacyPopup: typeof import('./../components/privacy-popup/privacy-popup.vue')['default']
  }
}
